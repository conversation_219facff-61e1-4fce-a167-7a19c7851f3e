#!/usr/bin/env python3
"""
Smart Indexing Manager

Demonstrates and manages the intelligent indexing system that:
1. Detects changes automatically
2. Indexes only deltas
3. Monitors MCP servers
4. Provides real-time status
"""

import asyncio
import sys
import os
import time
import argparse
from datetime import datetime
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.smart_indexing import SmartIndexingService, ChangeType
from services.vector_db.factory import VectorStoreFactory
from services.embedding_service import ToolEmbedder
from services.tool_discovery import ToolDiscoveryService
from services.mcp_registry import MCPServerRegistry
from config.mcp_config import MCPConfiguration
import structlog

logger = structlog.get_logger()


async def initialize_services():
    """Initialize all required services."""
    print("🔧 Initializing services...")
    
    # Initialize MCP registry and configuration
    config = MCPConfiguration()
    registry = MCPServerRegistry()
    
    # Register servers from config
    for server_name, server_config in config.mcp_servers.items():
        try:
            await registry.register_server(server_config)
            print(f"   ✅ Registered server: {server_name}")
        except Exception as e:
            print(f"   ❌ Failed to register {server_name}: {e}")
    
    # Initialize tool discovery
    discovery = ToolDiscoveryService(registry)
    
    # Initialize embedder (auto-selects best provider)
    embedder = ToolEmbedder(provider="auto")
    print(f"   📊 Embedder: {embedder.active_provider} ({embedder.model_name})")
    
    # Initialize vector store
    db_config = {
        "backend": "sqlite",
        "db_path": "data/ai_workspace_vectors.db",
        "embedding_dimension": embedder.dimension
    }
    vector_store = VectorStoreFactory.create(db_config)
    await vector_store.initialize()
    
    # Initialize smart indexing service
    smart_indexer = SmartIndexingService(
        vector_store=vector_store,
        tool_discovery=discovery,
        embedder=embedder,
        registry=registry,
        change_detection_interval=60,  # Check every minute for demo
        batch_size=16
    )
    
    print("✅ All services initialized")
    return smart_indexer


async def cmd_status(smart_indexer: SmartIndexingService):
    """Show current indexing status."""
    print("\n📊 SMART INDEXING STATUS")
    print("=" * 50)
    
    status = await smart_indexer.get_indexing_status()
    
    # Vector store status
    vs_status = status.get("vector_store", {})
    print(f"Vector Store:")
    print(f"  Total tools: {vs_status.get('total_tools', 0)}")
    print(f"  Healthy: {'✅' if vs_status.get('healthy') else '❌'}")
    
    # Monitoring status
    mon_status = status.get("monitoring", {})
    print(f"\nMonitoring:")
    print(f"  Active: {'✅' if mon_status.get('active') else '❌'}")
    print(f"  Check interval: {mon_status.get('check_interval_seconds', 0)}s")
    last_scan = mon_status.get('last_full_scan')
    if last_scan:
        print(f"  Last full scan: {last_scan.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Embedding status
    emb_status = status.get("embedding", {})
    print(f"\nEmbedding:")
    print(f"  Provider: {emb_status.get('provider', 'unknown')}")
    print(f"  Model: {emb_status.get('model', 'unknown')}")
    print(f"  Dimension: {emb_status.get('dimension', 0)}")
    
    # Server status
    servers = status.get("servers", {})
    print(f"\nMCP Servers ({len(servers)}):")
    for server_name, server_info in servers.items():
        status_icon = "✅" if server_info.get("status") == "connected" else "❌"
        tool_count = server_info.get("tool_count", 0)
        print(f"  {status_icon} {server_name}: {tool_count} tools")


async def cmd_detect_changes(smart_indexer: SmartIndexingService):
    """Detect changes without applying them."""
    print("\n🔍 DETECTING CHANGES")
    print("=" * 30)
    
    start_time = time.time()
    changes = await smart_indexer.detect_changes()
    detection_time = time.time() - start_time
    
    # Group changes by type
    by_type = {}
    for change in changes:
        change_type = change.change_type
        if change_type not in by_type:
            by_type[change_type] = []
        by_type[change_type].append(change)
    
    print(f"Detection completed in {detection_time:.2f}s")
    print(f"Total changes: {len(changes)}")
    
    for change_type, change_list in by_type.items():
        if change_type == ChangeType.UNCHANGED:
            continue  # Skip unchanged for brevity
        
        print(f"\n{change_type.value.upper()} ({len(change_list)}):")
        for change in change_list[:5]:  # Show first 5
            if change.new_tool:
                print(f"  • {change.new_tool.name} ({change.new_tool.server_name})")
            else:
                print(f"  • {change.tool_id}")
        
        if len(change_list) > 5:
            print(f"  ... and {len(change_list) - 5} more")


async def cmd_smart_update(smart_indexer: SmartIndexingService, force: bool = False):
    """Perform smart indexing update."""
    print(f"\n🚀 SMART INDEX UPDATE {'(FORCED)' if force else ''}")
    print("=" * 40)
    
    start_time = time.time()
    stats = await smart_indexer.smart_index_update(force_full_scan=force)
    total_time = time.time() - start_time
    
    print(f"✅ Update completed in {total_time:.2f}s")
    print(f"   Tools discovered: {stats.total_tools_discovered}")
    print(f"   Added: {stats.tools_added}")
    print(f"   Modified: {stats.tools_modified}")
    print(f"   Removed: {stats.tools_removed}")
    print(f"   Unchanged: {stats.tools_unchanged}")
    
    if stats.tools_added + stats.tools_modified > 0:
        indexing_speed = (stats.tools_added + stats.tools_modified) / stats.indexing_time_seconds
        print(f"   Indexing speed: {indexing_speed:.1f} tools/second")


async def cmd_index_server(smart_indexer: SmartIndexingService, server_name: str):
    """Index a specific server."""
    print(f"\n🎯 INDEXING SERVER: {server_name}")
    print("=" * 40)
    
    start_time = time.time()
    stats = await smart_indexer.index_server(server_name)
    total_time = time.time() - start_time
    
    print(f"✅ Server indexing completed in {total_time:.2f}s")
    print(f"   Added: {stats.tools_added}")
    print(f"   Modified: {stats.tools_modified}")
    print(f"   Unchanged: {stats.tools_unchanged}")


async def cmd_monitor(smart_indexer: SmartIndexingService, duration: int = 300):
    """Start monitoring for a specified duration."""
    print(f"\n👁️  STARTING MONITORING ({duration}s)")
    print("=" * 40)
    
    await smart_indexer.start_monitoring()
    print("Monitoring started... Press Ctrl+C to stop early")
    
    try:
        await asyncio.sleep(duration)
        print(f"\n⏰ Monitoring duration ({duration}s) completed")
    except KeyboardInterrupt:
        print(f"\n⏹️  Monitoring stopped by user")
    finally:
        await smart_indexer.stop_monitoring()
        print("Monitoring stopped")


async def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Smart Indexing Manager")
    parser.add_argument("command", choices=[
        "status", "detect", "update", "force-update", 
        "index-server", "monitor", "demo"
    ], help="Command to execute")
    parser.add_argument("--server", help="Server name for index-server command")
    parser.add_argument("--duration", type=int, default=300, 
                       help="Duration for monitor command (seconds)")
    
    args = parser.parse_args()
    
    # Initialize services
    smart_indexer = await initialize_services()
    
    try:
        if args.command == "status":
            await cmd_status(smart_indexer)
        
        elif args.command == "detect":
            await cmd_detect_changes(smart_indexer)
        
        elif args.command == "update":
            await cmd_smart_update(smart_indexer, force=False)
        
        elif args.command == "force-update":
            await cmd_smart_update(smart_indexer, force=True)
        
        elif args.command == "index-server":
            if not args.server:
                print("❌ --server argument required for index-server command")
                return
            await cmd_index_server(smart_indexer, args.server)
        
        elif args.command == "monitor":
            await cmd_monitor(smart_indexer, args.duration)
        
        elif args.command == "demo":
            await demo_smart_indexing(smart_indexer)
    
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def demo_smart_indexing(smart_indexer: SmartIndexingService):
    """Demonstrate smart indexing capabilities."""
    print("\n🎬 SMART INDEXING DEMO")
    print("=" * 50)
    
    # 1. Show initial status
    print("\n1. Initial Status:")
    await cmd_status(smart_indexer)
    
    # 2. Detect changes
    print("\n2. Change Detection:")
    await cmd_detect_changes(smart_indexer)
    
    # 3. Perform smart update
    print("\n3. Smart Update:")
    await cmd_smart_update(smart_indexer, force=False)
    
    # 4. Show final status
    print("\n4. Final Status:")
    await cmd_status(smart_indexer)
    
    print("\n🎉 Demo completed!")
    print("\nNext steps:")
    print("  • Run 'python scripts/smart_indexing_manager.py monitor' for continuous monitoring")
    print("  • Use 'update' for regular delta updates")
    print("  • Use 'force-update' when embedding model changes")


if __name__ == "__main__":
    asyncio.run(main())
