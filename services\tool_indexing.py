"""
Tool Indexing Pipeline

Indexes tools from MCP servers into the vector database for
intelligent filtering and semantic search.
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from .vector_db import VectorStore
from .embedding_service import ToolEmbedder
from .tool_discovery import ToolDiscoveryService, UnifiedToolSchema

logger = structlog.get_logger(__name__)


class ToolIndexingPipeline:
    """
    Indexes tools from MCP servers into vector database.
    
    Integrates with the existing ToolDiscoveryService to:
    1. Discover tools from all MCP servers
    2. Generate embeddings for each tool
    3. Store in vector database for fast retrieval
    """
    
    def __init__(
        self,
        vector_store: VectorStore,
        tool_discovery: Optional[ToolDiscoveryService] = None,
        embedder: Optional[ToolEmbedder] = None
    ):
        self.vector_store = vector_store
        self.tool_discovery = tool_discovery
        self.embedder = embedder or ToolEmbedder()
        self._indexing_lock = asyncio.Lock()
        
        logger.info("ToolIndexingPipeline initialized")
    
    async def index_all_tools(self) -> Dict[str, Any]:
        """
        Index all tools from all registered MCP servers.
        
        Returns:
            Statistics about the indexing operation
        """
        async with self._indexing_lock:
            logger.info("Starting full tool indexing")
            start_time = datetime.now()
            
            # Ensure vector store is initialized
            await self.vector_store.initialize()
            
            # Discover all tools
            if not self.tool_discovery:
                from .tool_discovery import get_tool_discovery_service
                self.tool_discovery = get_tool_discovery_service()
            
            tools = await self.tool_discovery.discover_all_tools()
            logger.info(f"Discovered {len(tools)} tools for indexing")
            
            # Index tools in batches
            batch_size = 32
            indexed_count = 0
            failed_count = 0
            
            for i in range(0, len(tools), batch_size):
                batch = tools[i:i + batch_size]
                
                try:
                    await self._index_tool_batch(batch)
                    indexed_count += len(batch)
                    
                    logger.info(
                        f"Indexed batch {i // batch_size + 1}/{(len(tools) + batch_size - 1) // batch_size}",
                        progress=f"{indexed_count}/{len(tools)}"
                    )
                    
                except Exception as e:
                    logger.error(f"Failed to index batch", error=str(e))
                    failed_count += len(batch)
            
            # Calculate statistics
            duration = (datetime.now() - start_time).total_seconds()
            
            stats = {
                "total_tools": len(tools),
                "indexed_count": indexed_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "tools_per_second": indexed_count / duration if duration > 0 else 0,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(
                "Tool indexing completed",
                **stats
            )
            
            return stats
    
    async def _index_tool_batch(self, tools: List[UnifiedToolSchema]):
        """Index a batch of tools."""
        # Prepare tool data for embedding
        tool_dicts = []
        for tool in tools:
            tool_dict = {
                "name": tool.name,
                "description": tool.description,
                "category": tool.category.value,
                "capabilities": [c.value for c in tool.capabilities],
                "keywords": tool.keywords,
                "parameters": tool.parameters,
                "examples": tool.examples,
                "server_name": tool.server_name
            }
            tool_dicts.append(tool_dict)
        
        # Generate embeddings for all tools in batch
        embeddings = await self.embedder.encode_tools(tool_dicts)
        
        # Store each tool with its embedding
        for i, tool in enumerate(tools):
            metadata = {
                "tool_name": tool.name,
                "server": tool.server_name,
                "description": tool.description,
                "category": tool.category.value,
                "parameters": tool.parameters,
                "examples": tool.examples,
                "capabilities": [c.value for c in tool.capabilities],
                "keywords": tool.keywords,
                "execution_stats": {
                    "count": 0,
                    "success_rate": 0.0,
                    "avg_execution_time": 0.0,
                    "last_used": None
                }
            }
            
            await self.vector_store.upsert(
                id=tool.full_name,
                embedding=embeddings[i],
                metadata=metadata
            )
    
    async def index_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """
        Index all tools from a specific MCP server.
        
        Args:
            server_name: Name of the MCP server to index
            
        Returns:
            Statistics about the indexing operation
        """
        logger.info(f"Indexing tools from MCP server: {server_name}")
        start_time = datetime.now()
        
        # Ensure vector store is initialized
        await self.vector_store.initialize()
        
        # Get tools from specific server
        if not self.tool_discovery:
            from .tool_discovery import get_tool_discovery_service
            self.tool_discovery = get_tool_discovery_service()
        
        # Search for tools from this server
        tools = await self.tool_discovery.search_tools(
            query="",
            server_name=server_name,
            limit=1000  # Get all tools from server
        )
        
        logger.info(f"Found {len(tools)} tools from {server_name}")
        
        # Index the tools
        indexed_count = 0
        failed_count = 0
        
        for tool in tools:
            try:
                await self._index_single_tool(tool)
                indexed_count += 1
            except Exception as e:
                logger.error(
                    f"Failed to index tool {tool.full_name}",
                    error=str(e)
                )
                failed_count += 1
        
        duration = (datetime.now() - start_time).total_seconds()
        
        stats = {
            "server_name": server_name,
            "total_tools": len(tools),
            "indexed_count": indexed_count,
            "failed_count": failed_count,
            "duration_seconds": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(
            f"Server indexing completed for {server_name}",
            **stats
        )
        
        return stats
    
    async def _index_single_tool(self, tool: UnifiedToolSchema):
        """Index a single tool."""
        # Prepare tool data
        tool_dict = {
            "name": tool.name,
            "description": tool.description,
            "category": tool.category.value,
            "capabilities": [c.value for c in tool.capabilities],
            "keywords": tool.keywords,
            "parameters": tool.parameters,
            "examples": tool.examples,
            "server_name": tool.server_name
        }
        
        # Generate embedding
        embedding = await self.embedder.encode_tool(tool_dict)
        
        # Prepare metadata
        metadata = {
            "tool_name": tool.name,
            "server": tool.server_name,
            "description": tool.description,
            "category": tool.category.value,
            "parameters": tool.parameters,
            "examples": tool.examples,
            "capabilities": [c.value for c in tool.capabilities],
            "keywords": tool.keywords,
            "execution_stats": {
                "count": 0,
                "success_rate": 0.0,
                "avg_execution_time": 0.0,
                "last_used": None
            }
        }
        
        # Store in vector database
        await self.vector_store.upsert(
            id=tool.full_name,
            embedding=embedding,
            metadata=metadata
        )
    
    async def update_tool(self, tool_id: str, tool_data: Dict[str, Any]):
        """
        Update a single tool's embedding and metadata.
        
        Args:
            tool_id: Full tool name (server:tool_name)
            tool_data: Updated tool data
        """
        logger.info(f"Updating tool index: {tool_id}")
        
        # Generate new embedding
        embedding = await self.embedder.encode_tool(tool_data)
        
        # Update in vector store
        await self.vector_store.upsert(
            id=tool_id,
            embedding=embedding,
            metadata=tool_data
        )
    
    async def remove_tool(self, tool_id: str) -> bool:
        """
        Remove a tool from the index.
        
        Args:
            tool_id: Full tool name (server:tool_name)
            
        Returns:
            True if removed, False if not found
        """
        logger.info(f"Removing tool from index: {tool_id}")
        return await self.vector_store.delete(tool_id)
    
    async def reindex_all(self):
        """
        Clear and rebuild the entire index.
        
        This is useful when the embedding model or indexing
        strategy changes significantly.
        """
        logger.info("Starting full reindex (clearing existing data)")
        
        # Clear existing data
        await self.vector_store.clear()
        
        # Reindex everything
        return await self.index_all_tools()
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the current index."""
        count = await self.vector_store.count()
        healthy = await self.vector_store.health_check()

        return {
            "total_indexed_tools": count,
            "vector_store_healthy": healthy,
            "embedding_model": self.embedder.model_name,
            "embedding_dimension": self.embedder.dimension
        }

    def create_smart_indexer(self, registry=None, change_detection_interval: int = 300):
        """
        Create a SmartIndexingService instance using this pipeline's components.

        Args:
            registry: MCP server registry (will be obtained from tool_discovery if None)
            change_detection_interval: How often to check for changes (seconds)

        Returns:
            SmartIndexingService instance
        """
        from .smart_indexing import SmartIndexingService

        # Get registry from tool discovery if not provided
        if registry is None and self.tool_discovery:
            registry = self.tool_discovery._registry

        if registry is None:
            raise ValueError("Registry required for smart indexing. Provide registry or ensure tool_discovery has one.")

        return SmartIndexingService(
            vector_store=self.vector_store,
            tool_discovery=self.tool_discovery,
            embedder=self.embedder,
            registry=registry,
            change_detection_interval=change_detection_interval
        )